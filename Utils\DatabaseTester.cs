using System;
using System.Windows.Forms;
using CarsSales.Data;
using CarsSales.Services;

namespace CarsSales.Utils
{
    public static class DatabaseTester
    {
        public static void TestDatabaseConnection()
        {
            try
            {
                // اختبار الاتصال
                if (DatabaseHelper.TestConnection())
                {
                    MessageBox.Show("✓ تم الاتصال بقاعدة البيانات بنجاح", 
                        "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("✗ فشل الاتصال بقاعدة البيانات", 
                        "فشل الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // اختبار جلب البيانات
                var saleCars = DatabaseService.GetAvailableCarsForSale();
                var rentalCars = DatabaseService.GetAvailableCarsForRental();

                string message = $"نتائج الاختبار:\n\n" +
                               $"✓ الاتصال بقاعدة البيانات: ناجح\n" +
                               $"✓ السيارات المعروضة للبيع: {saleCars.Count}\n" +
                               $"✓ السيارات المعروضة للإيجار: {rentalCars.Count}\n\n" +
                               $"قاعدة البيانات تعمل بشكل صحيح!";

                MessageBox.Show(message, "نتائج اختبار قاعدة البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار قاعدة البيانات:\n\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void ShowDatabaseInfo()
        {
            try
            {
                string connectionInfo = "معلومات الاتصال بقاعدة البيانات:\n\n" +
                                      "الخادم: CR7\n" +
                                      "قاعدة البيانات: CarsDB\n" +
                                      "نوع المصادقة: Windows Authentication\n\n" +
                                      "للتأكد من عمل الاتصال، اضغط على 'اختبار الاتصال'";

                DialogResult result = MessageBox.Show(connectionInfo, "معلومات قاعدة البيانات", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    TestDatabaseConnection();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
