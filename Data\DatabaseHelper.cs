using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.IO;

namespace CarsSales.Data
{
    public static class DatabaseHelper
    {
        private static readonly string connectionString = @"Data Source=CR7;Initial Catalog=CarsDB;Integrated Security=True";

        public static string GetConnectionString()
        {
            return connectionString;
        }

        public static DataTable ExecuteSelect(string query, params SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                if (parameters != null)
                    cmd.Parameters.AddRange(parameters);

                DataTable dt = new DataTable();
                conn.Open();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    dt.Load(reader);
                }
                return dt;
            }
        }

        public static int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                if (parameters != null)
                    cmd.Parameters.AddRange(parameters);

                conn.Open();
                return cmd.ExecuteNonQuery();
            }
        }

        public static object ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                if (parameters != null)
                    cmd.Parameters.AddRange(parameters);

                conn.Open();
                return cmd.ExecuteScalar();
            }
        }

        public static bool TestConnection()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    // Test with a simple query
                    using (SqlCommand cmd = new SqlCommand("SELECT 1", conn))
                    {
                        cmd.ExecuteScalar();
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Connection test failed: {ex.Message}",
                    "Database Connection Error", System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        public static void InitializeDatabase()
        {
            try
            {
                // First test if we can connect to the database
                if (!TestConnection())
                {
                    throw new Exception("Cannot connect to database server CR7");
                }

                // Try to create database if it doesn't exist
                CreateDatabaseIfNotExists();

                // Create tables if they don't exist
                CreateTables();

                // Insert initial data
                InsertInitialData();
            }
            catch (Exception ex)
            {
                throw new Exception($"Database initialization error: {ex.Message}");
            }
        }

        private static void CreateDatabaseIfNotExists()
        {
            try
            {
                string masterConnectionString = @"Data Source=CR7;Initial Catalog=master;Integrated Security=True";

                using (SqlConnection conn = new SqlConnection(masterConnectionString))
                {
                    conn.Open();

                    // Check if database exists
                    string checkDbQuery = "SELECT COUNT(*) FROM sys.databases WHERE name = 'CarsDB'";
                    using (SqlCommand cmd = new SqlCommand(checkDbQuery, conn))
                    {
                        int dbExists = (int)cmd.ExecuteScalar();
                        if (dbExists == 0)
                        {
                            // Create database
                            string createDbQuery = "CREATE DATABASE CarsDB";
                            using (SqlCommand createCmd = new SqlCommand(createDbQuery, conn))
                            {
                                createCmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error creating database: {ex.Message}");
            }
        }

        private static void CreateTables()
        {
            string createTablesScript = @"
                -- جدول المدراء
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Managers' AND xtype='U')
                CREATE TABLE Managers (
                    ManagerId INT IDENTITY(1,1) PRIMARY KEY,
                    Username NVARCHAR(50) UNIQUE NOT NULL,
                    PasswordHash NVARCHAR(255) NOT NULL,
                    FullName NVARCHAR(100) NOT NULL,
                    CreatedAt DATETIME DEFAULT GETDATE()
                );

                -- جدول العملاء
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
                CREATE TABLE Customers (
                    CustomerId INT IDENTITY(1,1) PRIMARY KEY,
                    FullName NVARCHAR(100) NOT NULL,
                    PhoneNumber NVARCHAR(20) UNIQUE NOT NULL,
                    PasswordHash NVARCHAR(255) NOT NULL,
                    CreatedAt DATETIME DEFAULT GETDATE()
                );

                -- جدول أصحاب السيارات (للسيارات المودعة للبيع)
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CarOwners' AND xtype='U')
                CREATE TABLE CarOwners (
                    OwnerId INT IDENTITY(1,1) PRIMARY KEY,
                    FullName NVARCHAR(100) NOT NULL,
                    PhoneNumber NVARCHAR(20) NOT NULL,
                    NationalId NVARCHAR(20),
                    Address NVARCHAR(200),
                    CommissionPercentage DECIMAL(5,2) DEFAULT 10.00,
                    CreatedAt DATETIME DEFAULT GETDATE()
                );

                -- جدول السيارات الرئيسي
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Cars' AND xtype='U')
                CREATE TABLE Cars (
                    CarId INT IDENTITY(1,1) PRIMARY KEY,
                    Brand NVARCHAR(50) NOT NULL,
                    Model NVARCHAR(50) NOT NULL,
                    Year INT NOT NULL,
                    Color NVARCHAR(30) NOT NULL,
                    Transmission NVARCHAR(20) NOT NULL CHECK (Transmission IN ('Manual', 'Automatic')),
                    MainImagePath NVARCHAR(500),
                    Defects NVARCHAR(1000),
                    Features NVARCHAR(1000),
                    OwnerId INT NULL,
                    Status NVARCHAR(20) DEFAULT 'Available' CHECK (Status IN ('Available', 'Reserved', 'Sold', 'Rented', 'Maintenance')),
                    CreatedAt DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (OwnerId) REFERENCES CarOwners(OwnerId)
                );

                -- جدول صور السيارات
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CarImages' AND xtype='U')
                CREATE TABLE CarImages (
                    ImageId INT IDENTITY(1,1) PRIMARY KEY,
                    CarId INT NOT NULL,
                    ImagePath NVARCHAR(500) NOT NULL,
                    ImageType NVARCHAR(20) DEFAULT 'General' CHECK (ImageType IN ('Main', 'Interior', 'Exterior', 'Engine', 'General')),
                    CreatedAt DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (CarId) REFERENCES Cars(CarId) ON DELETE CASCADE
                );

                -- جدول السيارات المعروضة للبيع
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SaleCars' AND xtype='U')
                CREATE TABLE SaleCars (
                    SaleCarId INT IDENTITY(1,1) PRIMARY KEY,
                    CarId INT NOT NULL,
                    Price DECIMAL(12,2) NOT NULL,
                    IsNegotiable BIT DEFAULT 0,
                    OwnershipDocument NVARCHAR(500),
                    Notes NVARCHAR(500),
                    CreatedAt DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (CarId) REFERENCES Cars(CarId) ON DELETE CASCADE
                );

                -- جدول السيارات المعروضة للإيجار
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RentalCars' AND xtype='U')
                CREATE TABLE RentalCars (
                    RentalCarId INT IDENTITY(1,1) PRIMARY KEY,
                    CarId INT NOT NULL,
                    DailyRate DECIMAL(8,2) NOT NULL,
                    MonthlyRate DECIMAL(10,2) NOT NULL,
                    YearlyRate DECIMAL(12,2) NOT NULL,
                    Notes NVARCHAR(500),
                    CreatedAt DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (CarId) REFERENCES Cars(CarId) ON DELETE CASCADE
                );

                -- جدول الحجوزات المؤقتة
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Reservations' AND xtype='U')
                CREATE TABLE Reservations (
                    ReservationId INT IDENTITY(1,1) PRIMARY KEY,
                    CarId INT NOT NULL,
                    CustomerId INT NOT NULL,
                    ReservationType NVARCHAR(10) NOT NULL CHECK (ReservationType IN ('Sale', 'Rental')),
                    ReservationDate DATETIME DEFAULT GETDATE(),
                    ExpiryDate DATETIME NOT NULL,
                    Status NVARCHAR(20) DEFAULT 'Active' CHECK (Status IN ('Active', 'Expired', 'Confirmed', 'Cancelled')),
                    Notes NVARCHAR(500),
                    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
                    FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId)
                );

                -- جدول العقود
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Contracts' AND xtype='U')
                CREATE TABLE Contracts (
                    ContractId INT IDENTITY(1,1) PRIMARY KEY,
                    CarId INT NOT NULL,
                    CustomerId INT NOT NULL,
                    ContractType NVARCHAR(10) NOT NULL CHECK (ContractType IN ('Sale', 'Rental')),
                    StartDate DATETIME NOT NULL,
                    EndDate DATETIME NULL,
                    Amount DECIMAL(12,2) NOT NULL,
                    CommissionAmount DECIMAL(12,2) DEFAULT 0,
                    Status NVARCHAR(20) DEFAULT 'Active' CHECK (Status IN ('Active', 'Completed', 'Cancelled')),
                    Notes NVARCHAR(500),
                    CreatedAt DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
                    FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId)
                );

                -- جدول العمليات المالية
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FinancialTransactions' AND xtype='U')
                CREATE TABLE FinancialTransactions (
                    TransactionId INT IDENTITY(1,1) PRIMARY KEY,
                    ContractId INT NULL,
                    TransactionType NVARCHAR(20) NOT NULL CHECK (TransactionType IN ('Sale', 'Rental', 'Commission', 'Expense')),
                    Amount DECIMAL(12,2) NOT NULL,
                    Description NVARCHAR(200),
                    TransactionDate DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (ContractId) REFERENCES Contracts(ContractId)
                );
            ";

            ExecuteNonQuery(createTablesScript);
        }

        private static void InsertInitialData()
        {
            // إدراج مدير افتراضي
            string checkManagerQuery = "SELECT COUNT(*) FROM Managers WHERE Username = 'admin'";
            int managerExists = (int)ExecuteScalar(checkManagerQuery);

            if (managerExists == 0)
            {
                string insertManagerQuery = @"
                    INSERT INTO Managers (Username, PasswordHash, FullName)
                    VALUES ('admin', 'admin123', N'مدير النظام')";
                ExecuteNonQuery(insertManagerQuery);
            }
        }
    }
} 