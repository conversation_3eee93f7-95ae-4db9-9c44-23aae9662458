using System;

namespace CarsSales.Models
{
    public class FinancialTransaction
    {
        public int TransactionId { get; set; }
        public int? ContractId { get; set; }
        public string TransactionType { get; set; } // Sale, Rental, Commission, Expense
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public DateTime TransactionDate { get; set; }

        // Navigation property
        public Contract Contract { get; set; }

        public FinancialTransaction()
        {
            TransactionDate = DateTime.Now;
        }
    }
}