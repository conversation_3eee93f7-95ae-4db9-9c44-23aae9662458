namespace CarsSales.Forms
{
    partial class CarForm
    {
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.Label labelName;
        private System.Windows.Forms.Label labelModel;
        private System.Windows.Forms.Label labelYear;
        private System.Windows.Forms.Label labelColor;
        private System.Windows.Forms.Label labelTransmission;
        private System.Windows.Forms.Label labelPrice;
        private System.Windows.Forms.Label labelNotes;
        private System.Windows.Forms.Label labelImage;
        private System.Windows.Forms.Label labelType;
        private System.Windows.Forms.Label labelRentPrices;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.TextBox txtModel;
        private System.Windows.Forms.TextBox txtYear;
        private System.Windows.Forms.TextBox txtColor;
        private System.Windows.Forms.ComboBox cmbTransmission;
        private System.Windows.Forms.TextBox txtPrice;
        private System.Windows.Forms.TextBox txtNotes;
        private System.Windows.Forms.TextBox txtImagePath;
        private System.Windows.Forms.ComboBox cmbType;
        private System.Windows.Forms.TextBox txtDailyPrice;
        private System.Windows.Forms.TextBox txtMonthlyPrice;
        private System.Windows.Forms.TextBox txtYearlyPrice;
        private System.Windows.Forms.Button btnBrowseImage;
        private System.Windows.Forms.Button btnSave;

        private void InitializeComponent()
        {
            this.labelName = new System.Windows.Forms.Label();
            this.labelModel = new System.Windows.Forms.Label();
            this.labelYear = new System.Windows.Forms.Label();
            this.labelColor = new System.Windows.Forms.Label();
            this.labelTransmission = new System.Windows.Forms.Label();
            this.labelPrice = new System.Windows.Forms.Label();
            this.labelNotes = new System.Windows.Forms.Label();
            this.labelImage = new System.Windows.Forms.Label();
            this.labelType = new System.Windows.Forms.Label();
            this.labelRentPrices = new System.Windows.Forms.Label();
            this.txtName = new System.Windows.Forms.TextBox();
            this.txtModel = new System.Windows.Forms.TextBox();
            this.txtYear = new System.Windows.Forms.TextBox();
            this.txtColor = new System.Windows.Forms.TextBox();
            this.cmbTransmission = new System.Windows.Forms.ComboBox();
            this.txtPrice = new System.Windows.Forms.TextBox();
            this.txtNotes = new System.Windows.Forms.TextBox();
            this.txtImagePath = new System.Windows.Forms.TextBox();
            this.cmbType = new System.Windows.Forms.ComboBox();
            this.txtDailyPrice = new System.Windows.Forms.TextBox();
            this.txtMonthlyPrice = new System.Windows.Forms.TextBox();
            this.txtYearlyPrice = new System.Windows.Forms.TextBox();
            this.btnBrowseImage = new System.Windows.Forms.Button();
            this.btnSave = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // labelName
            // 
            this.labelName.Text = "اسم السيارة:";
            this.labelName.Location = new System.Drawing.Point(30, 30);
            // 
            // labelModel
            // 
            this.labelModel.Text = "الموديل:";
            this.labelModel.Location = new System.Drawing.Point(30, 70);
            // 
            // labelYear
            // 
            this.labelYear.Text = "سنة التصنيع:";
            this.labelYear.Location = new System.Drawing.Point(30, 110);
            // 
            // labelColor
            // 
            this.labelColor.Text = "اللون:";
            this.labelColor.Location = new System.Drawing.Point(30, 150);
            // 
            // labelTransmission
            // 
            this.labelTransmission.Text = "ناقل الحركة:";
            this.labelTransmission.Location = new System.Drawing.Point(30, 190);
            // 
            // labelPrice
            // 
            this.labelPrice.Text = "السعر:";
            this.labelPrice.Location = new System.Drawing.Point(30, 230);
            // 
            // labelNotes
            // 
            this.labelNotes.Text = "ملاحظات:";
            this.labelNotes.Location = new System.Drawing.Point(30, 270);
            // 
            // labelImage
            // 
            this.labelImage.Text = "الصورة الرئيسية:";
            this.labelImage.Location = new System.Drawing.Point(30, 310);
            // 
            // labelType
            // 
            this.labelType.Text = "نوع السيارة:";
            this.labelType.Location = new System.Drawing.Point(30, 350);
            // 
            // labelRentPrices
            // 
            this.labelRentPrices.Text = "أسعار الإيجار (يومي/شهري/سنوي):";
            this.labelRentPrices.Location = new System.Drawing.Point(30, 390);
            // 
            // txtName
            // 
            this.txtName.Location = new System.Drawing.Point(150, 30);
            this.txtName.Size = new System.Drawing.Size(180, 20);
            // 
            // txtModel
            // 
            this.txtModel.Location = new System.Drawing.Point(150, 70);
            this.txtModel.Size = new System.Drawing.Size(180, 20);
            // 
            // txtYear
            // 
            this.txtYear.Location = new System.Drawing.Point(150, 110);
            this.txtYear.Size = new System.Drawing.Size(180, 20);
            // 
            // txtColor
            // 
            this.txtColor.Location = new System.Drawing.Point(150, 150);
            this.txtColor.Size = new System.Drawing.Size(180, 20);
            // 
            // cmbTransmission
            // 
            this.cmbTransmission.Location = new System.Drawing.Point(150, 190);
            this.cmbTransmission.Size = new System.Drawing.Size(180, 20);
            this.cmbTransmission.Items.AddRange(new object[] {"يدوي", "أوتوماتيكي"});
            // 
            // txtPrice
            // 
            this.txtPrice.Location = new System.Drawing.Point(150, 230);
            this.txtPrice.Size = new System.Drawing.Size(180, 20);
            // 
            // txtNotes
            // 
            this.txtNotes.Location = new System.Drawing.Point(150, 270);
            this.txtNotes.Size = new System.Drawing.Size(180, 20);
            // 
            // txtImagePath
            // 
            this.txtImagePath.Location = new System.Drawing.Point(150, 310);
            this.txtImagePath.Size = new System.Drawing.Size(140, 20);
            // 
            // btnBrowseImage
            // 
            this.btnBrowseImage.Text = "استعراض...";
            this.btnBrowseImage.Location = new System.Drawing.Point(300, 310);
            this.btnBrowseImage.Size = new System.Drawing.Size(80, 20);
            // 
            // cmbType
            // 
            this.cmbType.Location = new System.Drawing.Point(150, 350);
            this.cmbType.Size = new System.Drawing.Size(180, 20);
            this.cmbType.Items.AddRange(new object[] {"بيع", "إيجار"});
            // 
            // txtDailyPrice
            // 
            this.txtDailyPrice.Location = new System.Drawing.Point(150, 390);
            this.txtDailyPrice.Size = new System.Drawing.Size(60, 20);
            // 
            // txtMonthlyPrice
            // 
            this.txtMonthlyPrice.Location = new System.Drawing.Point(220, 390);
            this.txtMonthlyPrice.Size = new System.Drawing.Size(60, 20);
            // 
            // txtYearlyPrice
            // 
            this.txtYearlyPrice.Location = new System.Drawing.Point(290, 390);
            this.txtYearlyPrice.Size = new System.Drawing.Size(60, 20);
            // 
            // btnSave
            // 
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new System.Drawing.Point(150, 430);
            this.btnSave.Size = new System.Drawing.Size(80, 30);
            // 
            // CarForm
            // 
            this.ClientSize = new System.Drawing.Size(400, 480);
            this.Controls.Add(this.labelName);
            this.Controls.Add(this.labelModel);
            this.Controls.Add(this.labelYear);
            this.Controls.Add(this.labelColor);
            this.Controls.Add(this.labelTransmission);
            this.Controls.Add(this.labelPrice);
            this.Controls.Add(this.labelNotes);
            this.Controls.Add(this.labelImage);
            this.Controls.Add(this.labelType);
            this.Controls.Add(this.labelRentPrices);
            this.Controls.Add(this.txtName);
            this.Controls.Add(this.txtModel);
            this.Controls.Add(this.txtYear);
            this.Controls.Add(this.txtColor);
            this.Controls.Add(this.cmbTransmission);
            this.Controls.Add(this.txtPrice);
            this.Controls.Add(this.txtNotes);
            this.Controls.Add(this.txtImagePath);
            this.Controls.Add(this.btnBrowseImage);
            this.Controls.Add(this.cmbType);
            this.Controls.Add(this.txtDailyPrice);
            this.Controls.Add(this.txtMonthlyPrice);
            this.Controls.Add(this.txtYearlyPrice);
            this.Controls.Add(this.btnSave);
            this.Text = "إضافة/تعديل سيارة";
            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
} 