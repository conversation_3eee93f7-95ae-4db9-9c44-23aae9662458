using System;

namespace CarsSales.Models
{
    public class Customer
    {
        public int CustomerId { get; set; }
        public string FullName { get; set; }
        public string PhoneNumber { get; set; }
        public string PasswordHash { get; set; }
        public DateTime CreatedAt { get; set; }

        public Customer()
        {
            CreatedAt = DateTime.Now;
        }
    }

    public class Manager
    {
        public int ManagerId { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string FullName { get; set; }
        public DateTime CreatedAt { get; set; }

        public Manager()
        {
            CreatedAt = DateTime.Now;
        }
    }
}