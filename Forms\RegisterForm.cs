using System;
using System.Data;
using System.Windows.Forms;
using CarsSales.Data;

namespace CarsSales.Forms
{
    public partial class RegisterForm : Form
    {
        public RegisterForm()
        {
            InitializeComponent();
        }

        private void btnRegister_Click(object sender, EventArgs e)
        {
            string fullName = txtFullName.Text.Trim();
            string phone = txtPhone.Text.Trim();
            string password = txtPassword.Text;
            string confirmPassword = txtConfirmPassword.Text;

            if (string.IsNullOrEmpty(fullName) || string.IsNullOrEmpty(phone) || string.IsNullOrEmpty(password))
            {
                MessageBox.Show("يرجى تعبئة جميع الحقول.");
                return;
            }
            if (password != confirmPassword)
            {
                MessageBox.Show("كلمتا السر غير متطابقتين.");
                return;
            }
            // تحقق من عدم تكرار الاسم أو رقم الهاتف
            string checkQuery = "SELECT COUNT(*) FROM Users WHERE FullName = @name OR PhoneNumber = @phone";
            var exists = (int)DatabaseHelper.ExecuteScalar(checkQuery,
                new System.Data.SqlClient.SqlParameter("@name", fullName),
                new System.Data.SqlClient.SqlParameter("@phone", phone));
            if (exists > 0)
            {
                MessageBox.Show("الاسم أو رقم الهاتف مستخدم مسبقاً.");
                return;
            }
            // إضافة المستخدم
            string insertQuery = "INSERT INTO Users (FullName, PhoneNumber, PasswordHash) VALUES (@name, @phone, @pass)";
            int rows = DatabaseHelper.ExecuteNonQuery(insertQuery,
                new System.Data.SqlClient.SqlParameter("@name", fullName),
                new System.Data.SqlClient.SqlParameter("@phone", phone),
                new System.Data.SqlClient.SqlParameter("@pass", password));
            if (rows > 0)
            {
                MessageBox.Show("تم التسجيل بنجاح!");
                this.Close();
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء التسجيل.");
            }
        }
    }
} 