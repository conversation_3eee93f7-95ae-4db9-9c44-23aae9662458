<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{654858FD-09B6-4AD9-B387-A98B0866DF1E}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>CarsSales</RootNamespace>
    <AssemblyName>CarsSales</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Data\DatabaseHelper.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AdminDashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AdminDashboardForm.Designer.cs" />
    <Compile Include="Forms\CarDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CarDetailsForm.Designer.cs" />
    <Compile Include="Forms\CarForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CarForm.Designer.cs" />
    <Compile Include="Forms\CarsListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CarsListForm.Designer.cs" />
    <Compile Include="Forms\CarsManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CarsManagementForm.Designer.cs" />
    <Compile Include="Forms\ContractsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ContractsForm.Designer.cs" />
    <Compile Include="Forms\FinancialForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FinancialForm.Designer.cs" />
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs" />
    <Compile Include="Forms\RegisterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\RegisterForm.Designer.cs" />
    <Compile Include="Forms\UserDashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserDashboardForm.Designer.cs" />
    <Compile Include="Models\Car.cs" />
    <Compile Include="Models\CarImage.cs" />
    <Compile Include="Models\Contract.cs" />
    <Compile Include="Models\FinancialTransaction.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="Services\DatabaseService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8.1 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>