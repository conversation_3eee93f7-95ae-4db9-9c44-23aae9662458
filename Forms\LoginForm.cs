using System;
using System.Data;
using System.Windows.Forms;
using CarsSales.Data;
using CarsSales.Services;
using CarsSales.Utils;

namespace CarsSales.Forms
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                string usernameOrPhone = txtUsername.Text.Trim();
                string password = txtPassword.Text;

                if (string.IsNullOrEmpty(usernameOrPhone) || string.IsNullOrEmpty(password))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تحقق من المدير
                var manager = DatabaseService.AuthenticateManager(usernameOrPhone, password);
                if (manager != null)
                {
                    MessageBox.Show($"مرحباً بك {manager.FullName}!", "تسجيل دخول ناجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // افتح لوحة المدير
                    var adminForm = new AdminDashboardForm();
                    adminForm.Show();
                    this.Hide();
                    return;
                }

                // تحقق من العميل
                var customer = DatabaseService.AuthenticateCustomer(usernameOrPhone, password);
                if (customer != null)
                {
                    MessageBox.Show($"مرحباً بك {customer.FullName}!", "تسجيل دخول ناجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // افتح لوحة العميل
                    var userForm = new UserDashboardForm();
                    userForm.Show();
                    this.Hide();
                    return;
                }

                // فشل تسجيل الدخول
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                // this.Hide();
                return;
            }
        }

        private void btnRegister_Click(object sender, EventArgs e)
        {
            var registerForm = new RegisterForm();
            registerForm.ShowDialog();
        }

        private void btnTestDatabase_Click(object sender, EventArgs e)
        {
            DatabaseTester.TestDatabaseConnection();
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // اختبار الاتصال عند تحميل النموذج
            if (!DatabaseHelper.TestConnection())
            {
                MessageBox.Show("تحذير: لا يمكن الاتصال بقاعدة البيانات.\nتحقق من إعدادات الاتصال.",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
} 