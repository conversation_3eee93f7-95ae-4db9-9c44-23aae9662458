using System;
using System.Data;
using System.Windows.Forms;
using CarsSales.Data;

namespace CarsSales.Forms
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            string usernameOrPhone = txtUsername.Text.Trim();
            string password = txtPassword.Text;

            // تحقق من المدير
            string adminQuery = "SELECT * FROM Admins WHERE Username = @user AND PasswordHash = @pass";
            var admin = DatabaseHelper.ExecuteSelect(adminQuery,
                new System.Data.SqlClient.SqlParameter("@user", usernameOrPhone),
                new System.Data.SqlClient.SqlParameter("@pass", password));

            if (admin.Rows.Count > 0)
            {
                // افتح لوحة المدير
                MessageBox.Show("مرحباً بك مدير النظام!");
                // new AdminDashboardForm().Show();
                // this.Hide();
                return;
            }

            // تحقق من المستخدم
            string userQuery = "SELECT * FROM Users WHERE (PhoneNumber = @user OR FullName = @user) AND PasswordHash = @pass";
            var user = DatabaseHelper.ExecuteSelect(userQuery,
                new System.Data.SqlClient.SqlParameter("@user", usernameOrPhone),
                new System.Data.SqlClient.SqlParameter("@pass", password));

            if (user.Rows.Count > 0)
            {
                // افتح لوحة المستخدم
                MessageBox.Show("مرحباً بك في النظام!");
                // new UserDashboardForm().Show();
                // this.Hide();
                return;
            }

            MessageBox.Show("بيانات الدخول غير صحيحة!");
        }

        private void btnRegister_Click(object sender, EventArgs e)
        {
            // new RegisterForm().ShowDialog();
            MessageBox.Show("واجهة التسجيل لم تُنشأ بعد.");
        }
    }
} 