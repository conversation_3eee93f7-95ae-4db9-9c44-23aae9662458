using System;
using System.Collections.Generic;

namespace CarsSales.Models
{
    public class Car
    {
        public int CarId { get; set; }
        public string Brand { get; set; }
        public string Model { get; set; }
        public int Year { get; set; }
        public string Color { get; set; }
        public string Transmission { get; set; } // Manual or Automatic
        public string MainImagePath { get; set; }
        public string Defects { get; set; }
        public string Features { get; set; }
        public int? OwnerId { get; set; }
        public string Status { get; set; } // Available, Reserved, Sold, Rented, Maintenance
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        public CarOwner Owner { get; set; }
        public List<CarImage> Images { get; set; }
        public SaleCar SaleInfo { get; set; }
        public RentalCar RentalInfo { get; set; }

        public Car()
        {
            Images = new List<CarImage>();
            CreatedAt = DateTime.Now;
            Status = "Available";
        }
    }

    public class CarOwner
    {
        public int OwnerId { get; set; }
        public string FullName { get; set; }
        public string PhoneNumber { get; set; }
        public string NationalId { get; set; }
        public string Address { get; set; }
        public decimal CommissionPercentage { get; set; }
        public DateTime CreatedAt { get; set; }

        public CarOwner()
        {
            CreatedAt = DateTime.Now;
            CommissionPercentage = 10.00m;
        }
    }

    public class SaleCar
    {
        public int SaleCarId { get; set; }
        public int CarId { get; set; }
        public decimal Price { get; set; }
        public bool IsNegotiable { get; set; }
        public string OwnershipDocument { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation property
        public Car Car { get; set; }

        public SaleCar()
        {
            CreatedAt = DateTime.Now;
        }
    }

    public class RentalCar
    {
        public int RentalCarId { get; set; }
        public int CarId { get; set; }
        public decimal DailyRate { get; set; }
        public decimal MonthlyRate { get; set; }
        public decimal YearlyRate { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation property
        public Car Car { get; set; }

        public RentalCar()
        {
            CreatedAt = DateTime.Now;
        }
    }

    public class Reservation
    {
        public int ReservationId { get; set; }
        public int CarId { get; set; }
        public int CustomerId { get; set; }
        public string ReservationType { get; set; } // Sale or Rental
        public DateTime ReservationDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string Status { get; set; } // Active, Expired, Confirmed, Cancelled
        public string Notes { get; set; }

        // Navigation properties
        public Car Car { get; set; }
        public Customer Customer { get; set; }

        public Reservation()
        {
            ReservationDate = DateTime.Now;
            Status = "Active";
        }
    }
}