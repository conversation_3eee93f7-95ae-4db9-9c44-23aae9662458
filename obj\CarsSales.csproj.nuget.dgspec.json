{"format": 1, "restore": {"C:\\MyProjects\\WinForms\\CarsSales\\CarsSales.csproj": {}}, "projects": {"C:\\MyProjects\\WinForms\\CarsSales\\CarsSales.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\MyProjects\\WinForms\\CarsSales\\CarsSales.csproj", "projectName": "CarsSales", "projectPath": "C:\\MyProjects\\WinForms\\CarsSales\\CarsSales.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MyProjects\\WinForms\\CarsSales\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net481"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net481": {"targetAlias": "net481", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net481": {"targetAlias": "net481", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}