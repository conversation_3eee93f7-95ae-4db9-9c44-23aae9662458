@echo off
echo Building Cars Sales Application...

:: Clean previous builds
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj

:: Build the project
echo Building project...
dotnet build CarsSales.csproj --configuration Debug --verbosity minimal

if %errorlevel% equ 0 (
    echo Build successful! Starting application...
    echo.
    dotnet run --project CarsSales.csproj
) else (
    echo Build failed. Please check the errors above.
    pause
)
