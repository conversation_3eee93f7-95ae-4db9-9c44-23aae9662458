-- سكريبت إنشاء قاعدة بيانات مكتب السيارات
-- Cars Sales Database Creation Script

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'CarsDB')
BEGIN
    CREATE DATABASE CarsDB;
END
GO

USE CarsDB;
GO

-- جدول المدراء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Managers' AND xtype='U')
CREATE TABLE Managers (
    ManagerId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    CreatedAt DATETIME DEFAULT GETDATE()
);

-- جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
CREATE TABLE Customers (
    CustomerId INT IDENTITY(1,1) PRIMARY KEY,
    FullName NVARCHAR(100) NOT NULL,
    PhoneNumber NVARCHAR(20) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    CreatedAt DATETIME DEFAULT GETDATE()
);

-- جدول أصحاب السيارات (للسيارات المودعة للبيع)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CarOwners' AND xtype='U')
CREATE TABLE CarOwners (
    OwnerId INT IDENTITY(1,1) PRIMARY KEY,
    FullName NVARCHAR(100) NOT NULL,
    PhoneNumber NVARCHAR(20) NOT NULL,
    NationalId NVARCHAR(20),
    Address NVARCHAR(200),
    CommissionPercentage DECIMAL(5,2) DEFAULT 10.00,
    CreatedAt DATETIME DEFAULT GETDATE()
);

-- جدول السيارات الرئيسي
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Cars' AND xtype='U')
CREATE TABLE Cars (
    CarId INT IDENTITY(1,1) PRIMARY KEY,
    Brand NVARCHAR(50) NOT NULL,
    Model NVARCHAR(50) NOT NULL,
    Year INT NOT NULL,
    Color NVARCHAR(30) NOT NULL,
    Transmission NVARCHAR(20) NOT NULL CHECK (Transmission IN ('Manual', 'Automatic')),
    MainImagePath NVARCHAR(500),
    Defects NVARCHAR(1000),
    Features NVARCHAR(1000),
    OwnerId INT NULL,
    Status NVARCHAR(20) DEFAULT 'Available' CHECK (Status IN ('Available', 'Reserved', 'Sold', 'Rented', 'Maintenance')),
    CreatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (OwnerId) REFERENCES CarOwners(OwnerId)
);

-- جدول صور السيارات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CarImages' AND xtype='U')
CREATE TABLE CarImages (
    ImageId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    ImagePath NVARCHAR(500) NOT NULL,
    ImageType NVARCHAR(20) DEFAULT 'General' CHECK (ImageType IN ('Main', 'Interior', 'Exterior', 'Engine', 'General')),
    CreatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (CarId) REFERENCES Cars(CarId) ON DELETE CASCADE
);

-- جدول السيارات المعروضة للبيع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SaleCars' AND xtype='U')
CREATE TABLE SaleCars (
    SaleCarId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    Price DECIMAL(12,2) NOT NULL,
    IsNegotiable BIT DEFAULT 0,
    OwnershipDocument NVARCHAR(500),
    Notes NVARCHAR(500),
    CreatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (CarId) REFERENCES Cars(CarId) ON DELETE CASCADE
);

-- جدول السيارات المعروضة للإيجار
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RentalCars' AND xtype='U')
CREATE TABLE RentalCars (
    RentalCarId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    DailyRate DECIMAL(8,2) NOT NULL,
    MonthlyRate DECIMAL(10,2) NOT NULL,
    YearlyRate DECIMAL(12,2) NOT NULL,
    Notes NVARCHAR(500),
    CreatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (CarId) REFERENCES Cars(CarId) ON DELETE CASCADE
);

-- جدول الحجوزات المؤقتة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Reservations' AND xtype='U')
CREATE TABLE Reservations (
    ReservationId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    CustomerId INT NOT NULL,
    ReservationType NVARCHAR(10) NOT NULL CHECK (ReservationType IN ('Sale', 'Rental')),
    ReservationDate DATETIME DEFAULT GETDATE(),
    ExpiryDate DATETIME NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active' CHECK (Status IN ('Active', 'Expired', 'Confirmed', 'Cancelled')),
    Notes NVARCHAR(500),
    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
    FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId)
);

-- جدول العقود
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Contracts' AND xtype='U')
CREATE TABLE Contracts (
    ContractId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    CustomerId INT NOT NULL,
    ContractType NVARCHAR(10) NOT NULL CHECK (ContractType IN ('Sale', 'Rental')),
    StartDate DATETIME NOT NULL,
    EndDate DATETIME NULL,
    Amount DECIMAL(12,2) NOT NULL,
    CommissionAmount DECIMAL(12,2) DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Active' CHECK (Status IN ('Active', 'Completed', 'Cancelled')),
    Notes NVARCHAR(500),
    CreatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
    FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId)
);

-- جدول العمليات المالية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FinancialTransactions' AND xtype='U')
CREATE TABLE FinancialTransactions (
    TransactionId INT IDENTITY(1,1) PRIMARY KEY,
    ContractId INT NULL,
    TransactionType NVARCHAR(20) NOT NULL CHECK (TransactionType IN ('Sale', 'Rental', 'Commission', 'Expense')),
    Amount DECIMAL(12,2) NOT NULL,
    Description NVARCHAR(200),
    TransactionDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (ContractId) REFERENCES Contracts(ContractId)
);

-- إدراج البيانات الأولية
-- إدراج مدير افتراضي
IF NOT EXISTS (SELECT * FROM Managers WHERE Username = 'admin')
BEGIN
    INSERT INTO Managers (Username, PasswordHash, FullName) 
    VALUES ('admin', 'admin123', N'مدير النظام');
END

-- إدراج بيانات تجريبية للسيارات
IF NOT EXISTS (SELECT * FROM Cars WHERE CarId = 1)
BEGIN
    -- إدراج مالك سيارة تجريبي
    INSERT INTO CarOwners (FullName, PhoneNumber, NationalId, Address, CommissionPercentage)
    VALUES (N'أحمد محمد', '0501234567', '1234567890', N'الرياض - حي النخيل', 15.00);

    -- إدراج سيارة تجريبية
    INSERT INTO Cars (Brand, Model, Year, Color, Transmission, Defects, Features, OwnerId, Status)
    VALUES (N'تويوتا', N'كامري', 2020, N'أبيض', 'Automatic', N'لا توجد أعطال', N'مكيف - نوافذ كهربائية - مقاعد جلدية', 1, 'Available');

    -- إدراج معلومات البيع
    INSERT INTO SaleCars (CarId, Price, IsNegotiable, Notes)
    VALUES (1, 85000.00, 1, N'السعر قابل للتفاوض');

    -- إدراج سيارة للإيجار
    INSERT INTO Cars (Brand, Model, Year, Color, Transmission, Features, Status)
    VALUES (N'هيونداي', N'إلنترا', 2021, N'أسود', 'Automatic', N'مكيف - بلوتوث - كاميرا خلفية', 'Available');

    INSERT INTO RentalCars (CarId, DailyRate, MonthlyRate, YearlyRate, Notes)
    VALUES (2, 150.00, 4000.00, 45000.00, N'متاحة للإيجار اليومي والشهري');
END

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
