using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using CarsSales.Data;
using CarsSales.Models;

namespace CarsSales.Services
{
    public class DatabaseService
    {
        public static bool InitializeDatabase()
        {
            try
            {
                DatabaseHelper.InitializeDatabase();
                return true;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}",
                    "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        public static bool TestConnection()
        {
            return DatabaseHelper.TestConnection();
        }

        // Manager authentication services
        public static Manager AuthenticateManager(string username, string password)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SELECT ManagerId, Username, FullName, CreatedAt FROM Managers WHERE Username = @username AND PasswordHash = @password", connection))
                    {
                        command.Parameters.Add(new SqlParameter("@username", username));
                        command.Parameters.Add(new SqlParameter("@password", password));

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Manager
                                {
                                    ManagerId = reader.GetInt32("ManagerId"),
                                    Username = reader.GetString("Username"),
                                    FullName = reader.GetString("FullName"),
                                    CreatedAt = reader.GetDateTime("CreatedAt")
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"Manager authentication error: {ex.Message}");
            }
        }

        // Customer authentication services
        public static Customer AuthenticateCustomer(string phoneNumber, string password)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SELECT CustomerId, FullName, PhoneNumber, CreatedAt FROM Customers WHERE PhoneNumber = @phone AND PasswordHash = @password", connection))
                    {
                        command.Parameters.Add(new SqlParameter("@phone", phoneNumber));
                        command.Parameters.Add(new SqlParameter("@password", password));

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Customer
                                {
                                    CustomerId = reader.GetInt32("CustomerId"),
                                    FullName = reader.GetString("FullName"),
                                    PhoneNumber = reader.GetString("PhoneNumber"),
                                    CreatedAt = reader.GetDateTime("CreatedAt")
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"Customer authentication error: {ex.Message}");
            }
        }

        public static bool RegisterCustomer(Customer customer)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    connection.Open();

                    // Check if phone number already exists
                    using (var checkCommand = new SqlCommand("SELECT COUNT(*) FROM Customers WHERE PhoneNumber = @phone", connection))
                    {
                        checkCommand.Parameters.Add(new SqlParameter("@phone", customer.PhoneNumber));
                        int exists = (int)checkCommand.ExecuteScalar();

                        if (exists > 0)
                        {
                            throw new Exception("رقم الهاتف مسجل مسبقاً");
                        }
                    }

                    // Insert new customer
                    using (var insertCommand = new SqlCommand("INSERT INTO Customers (FullName, PhoneNumber, PasswordHash) VALUES (@name, @phone, @password)", connection))
                    {
                        insertCommand.Parameters.Add(new SqlParameter("@name", SqlDbType.NVarChar) { Value = customer.FullName });
                        insertCommand.Parameters.Add(new SqlParameter("@phone", SqlDbType.NVarChar) { Value = customer.PhoneNumber });
                        insertCommand.Parameters.Add(new SqlParameter("@password", SqlDbType.NVarChar) { Value = customer.PasswordHash });

                        int result = insertCommand.ExecuteNonQuery();
                        return result > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Customer registration error: {ex.Message}");
            }
        }

        // خدمات السيارات
        public static List<Car> GetAvailableCarsForSale()
        {
            try
            {
                string query = @"
                    SELECT c.*, sc.Price, sc.IsNegotiable, sc.Notes as SaleNotes, co.FullName as OwnerName
                    FROM Cars c
                    INNER JOIN SaleCars sc ON c.CarId = sc.CarId
                    LEFT JOIN CarOwners co ON c.OwnerId = co.OwnerId
                    WHERE c.Status = 'Available'
                    ORDER BY c.CreatedAt DESC";

                DataTable dt = DatabaseHelper.ExecuteSelect(query);
                List<Car> cars = new List<Car>();

                foreach (DataRow row in dt.Rows)
                {
                    var car = new Car
                    {
                        CarId = Convert.ToInt32(row["CarId"]),
                        Brand = row["Brand"].ToString(),
                        Model = row["Model"].ToString(),
                        Year = Convert.ToInt32(row["Year"]),
                        Color = row["Color"].ToString(),
                        Transmission = row["Transmission"].ToString(),
                        MainImagePath = row["MainImagePath"]?.ToString(),
                        Defects = row["Defects"]?.ToString(),
                        Features = row["Features"]?.ToString(),
                        Status = row["Status"].ToString(),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"])
                    };

                    car.SaleInfo = new SaleCar
                    {
                        CarId = car.CarId,
                        Price = Convert.ToDecimal(row["Price"]),
                        IsNegotiable = Convert.ToBoolean(row["IsNegotiable"]),
                        Notes = row["SaleNotes"]?.ToString()
                    };

                    cars.Add(car);
                }

                return cars;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب السيارات المعروضة للبيع: {ex.Message}");
            }
        }

        public static List<Car> GetAvailableCarsForRental()
        {
            try
            {
                string query = @"
                    SELECT c.*, rc.DailyRate, rc.MonthlyRate, rc.YearlyRate, rc.Notes as RentalNotes
                    FROM Cars c
                    INNER JOIN RentalCars rc ON c.CarId = rc.CarId
                    WHERE c.Status = 'Available'
                    ORDER BY c.CreatedAt DESC";

                DataTable dt = DatabaseHelper.ExecuteSelect(query);
                List<Car> cars = new List<Car>();

                foreach (DataRow row in dt.Rows)
                {
                    var car = new Car
                    {
                        CarId = Convert.ToInt32(row["CarId"]),
                        Brand = row["Brand"].ToString(),
                        Model = row["Model"].ToString(),
                        Year = Convert.ToInt32(row["Year"]),
                        Color = row["Color"].ToString(),
                        Transmission = row["Transmission"].ToString(),
                        MainImagePath = row["MainImagePath"]?.ToString(),
                        Features = row["Features"]?.ToString(),
                        Status = row["Status"].ToString(),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"])
                    };

                    car.RentalInfo = new RentalCar
                    {
                        CarId = car.CarId,
                        DailyRate = Convert.ToDecimal(row["DailyRate"]),
                        MonthlyRate = Convert.ToDecimal(row["MonthlyRate"]),
                        YearlyRate = Convert.ToDecimal(row["YearlyRate"]),
                        Notes = row["RentalNotes"]?.ToString()
                    };

                    cars.Add(car);
                }

                return cars;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب السيارات المعروضة للإيجار: {ex.Message}");
            }
        }

        public static bool AddCar(Car car, SaleCar saleInfo = null, RentalCar rentalInfo = null)
        {
            try
            {
                // إدراج السيارة
                string carQuery = @"
                    INSERT INTO Cars (Brand, Model, Year, Color, Transmission, MainImagePath, Defects, Features, OwnerId, Status)
                    VALUES (@brand, @model, @year, @color, @transmission, @mainImage, @defects, @features, @ownerId, @status);
                    SELECT SCOPE_IDENTITY();";

                var carParams = new SqlParameter[]
                {
                    new SqlParameter("@brand", car.Brand),
                    new SqlParameter("@model", car.Model),
                    new SqlParameter("@year", car.Year),
                    new SqlParameter("@color", car.Color),
                    new SqlParameter("@transmission", car.Transmission),
                    new SqlParameter("@mainImage", (object)car.MainImagePath ?? DBNull.Value),
                    new SqlParameter("@defects", (object)car.Defects ?? DBNull.Value),
                    new SqlParameter("@features", (object)car.Features ?? DBNull.Value),
                    new SqlParameter("@ownerId", (object)car.OwnerId ?? DBNull.Value),
                    new SqlParameter("@status", car.Status)
                };

                int carId = Convert.ToInt32(DatabaseHelper.ExecuteScalar(carQuery, carParams));

                // إدراج معلومات البيع إذا كانت موجودة
                if (saleInfo != null)
                {
                    string saleQuery = @"
                        INSERT INTO SaleCars (CarId, Price, IsNegotiable, OwnershipDocument, Notes)
                        VALUES (@carId, @price, @negotiable, @document, @notes)";

                    var saleParams = new SqlParameter[]
                    {
                        new SqlParameter("@carId", carId),
                        new SqlParameter("@price", saleInfo.Price),
                        new SqlParameter("@negotiable", saleInfo.IsNegotiable),
                        new SqlParameter("@document", (object)saleInfo.OwnershipDocument ?? DBNull.Value),
                        new SqlParameter("@notes", (object)saleInfo.Notes ?? DBNull.Value)
                    };

                    DatabaseHelper.ExecuteNonQuery(saleQuery, saleParams);
                }

                // إدراج معلومات الإيجار إذا كانت موجودة
                if (rentalInfo != null)
                {
                    string rentalQuery = @"
                        INSERT INTO RentalCars (CarId, DailyRate, MonthlyRate, YearlyRate, Notes)
                        VALUES (@carId, @daily, @monthly, @yearly, @notes)";

                    var rentalParams = new SqlParameter[]
                    {
                        new SqlParameter("@carId", carId),
                        new SqlParameter("@daily", rentalInfo.DailyRate),
                        new SqlParameter("@monthly", rentalInfo.MonthlyRate),
                        new SqlParameter("@yearly", rentalInfo.YearlyRate),
                        new SqlParameter("@notes", (object)rentalInfo.Notes ?? DBNull.Value)
                    };

                    DatabaseHelper.ExecuteNonQuery(rentalQuery, rentalParams);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة السيارة: {ex.Message}");
            }
        }
    }
}
