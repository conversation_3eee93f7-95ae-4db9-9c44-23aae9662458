using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using CarsSales.Data;
using CarsSales.Models;

namespace CarsSales.Services
{
    public class DatabaseService
    {
        public static bool InitializeDatabase()
        {
            try
            {
                DatabaseHelper.InitializeDatabase();
                return true;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}",
                    "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        public static bool TestConnection()
        {
            return DatabaseHelper.TestConnection();
        }

        // Manager authentication using direct SQL
        public static Manager AuthenticateManager(string username, string password)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    conn.Open();
                    string query = "SELECT ManagerId, Username, FullName, CreatedAt FROM Managers WHERE Username = @username AND PasswordHash = @password";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@username", username);
                        cmd.Parameters.AddWithValue("@password", password);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Manager
                                {
                                    ManagerId = (int)reader["ManagerId"],
                                    Username = reader["Username"].ToString(),
                                    FullName = reader["FullName"].ToString(),
                                    CreatedAt = (DateTime)reader["CreatedAt"]
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"Manager authentication error: {ex.Message}");
            }
        }

        // Customer authentication using direct SQL
        public static Customer AuthenticateCustomer(string phoneNumber, string password)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    conn.Open();
                    string query = "SELECT CustomerId, FullName, PhoneNumber, CreatedAt FROM Customers WHERE PhoneNumber = @phone AND PasswordHash = @password";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@phone", phoneNumber);
                        cmd.Parameters.AddWithValue("@password", password);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Customer
                                {
                                    CustomerId = (int)reader["CustomerId"],
                                    FullName = reader["FullName"].ToString(),
                                    PhoneNumber = reader["PhoneNumber"].ToString(),
                                    CreatedAt = (DateTime)reader["CreatedAt"]
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"Customer authentication error: {ex.Message}");
            }
        }

        public static bool RegisterCustomer(Customer customer)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    conn.Open();

                    // Check if phone number already exists
                    string checkQuery = "SELECT COUNT(*) FROM Customers WHERE PhoneNumber = @phone";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, conn))
                    {
                        checkCmd.Parameters.AddWithValue("@phone", customer.PhoneNumber);
                        int exists = (int)checkCmd.ExecuteScalar();

                        if (exists > 0)
                        {
                            throw new Exception("رقم الهاتف مسجل مسبقاً");
                        }
                    }

                    // Insert new customer
                    string insertQuery = "INSERT INTO Customers (FullName, PhoneNumber, PasswordHash) VALUES (@name, @phone, @password)";
                    using (SqlCommand insertCmd = new SqlCommand(insertQuery, conn))
                    {
                        insertCmd.Parameters.AddWithValue("@name", customer.FullName);
                        insertCmd.Parameters.AddWithValue("@phone", customer.PhoneNumber);
                        insertCmd.Parameters.AddWithValue("@password", customer.PasswordHash);

                        int result = insertCmd.ExecuteNonQuery();
                        return result > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Customer registration error: {ex.Message}");
            }
        }

        // Car services using direct SQL
        public static List<Car> GetAvailableCarsForSale()
        {
            try
            {
                List<Car> cars = new List<Car>();

                using (SqlConnection conn = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    conn.Open();
                    string query = @"
                        SELECT c.*, sc.Price, sc.IsNegotiable, sc.Notes as SaleNotes, co.FullName as OwnerName
                        FROM Cars c
                        INNER JOIN SaleCars sc ON c.CarId = sc.CarId
                        LEFT JOIN CarOwners co ON c.OwnerId = co.OwnerId
                        WHERE c.Status = 'Available'
                        ORDER BY c.CreatedAt DESC";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var car = new Car
                            {
                                CarId = (int)reader["CarId"],
                                Brand = reader["Brand"].ToString(),
                                Model = reader["Model"].ToString(),
                                Year = (int)reader["Year"],
                                Color = reader["Color"].ToString(),
                                Transmission = reader["Transmission"].ToString(),
                                MainImagePath = reader["MainImagePath"] == DBNull.Value ? null : reader["MainImagePath"].ToString(),
                                Defects = reader["Defects"] == DBNull.Value ? null : reader["Defects"].ToString(),
                                Features = reader["Features"] == DBNull.Value ? null : reader["Features"].ToString(),
                                Status = reader["Status"].ToString(),
                                CreatedAt = (DateTime)reader["CreatedAt"]
                            };

                            car.SaleInfo = new SaleCar
                            {
                                CarId = car.CarId,
                                Price = (decimal)reader["Price"],
                                IsNegotiable = (bool)reader["IsNegotiable"],
                                Notes = reader["SaleNotes"] == DBNull.Value ? null : reader["SaleNotes"].ToString()
                            };

                            cars.Add(car);
                        }
                    }
                }

                return cars;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting cars for sale: {ex.Message}");
            }
        }

        public static List<Car> GetAvailableCarsForRental()
        {
            try
            {
                List<Car> cars = new List<Car>();

                using (SqlConnection conn = new SqlConnection(DatabaseHelper.GetConnectionString()))
                {
                    conn.Open();
                    string query = @"
                        SELECT c.*, rc.DailyRate, rc.MonthlyRate, rc.YearlyRate, rc.Notes as RentalNotes
                        FROM Cars c
                        INNER JOIN RentalCars rc ON c.CarId = rc.CarId
                        WHERE c.Status = 'Available'
                        ORDER BY c.CreatedAt DESC";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var car = new Car
                            {
                                CarId = (int)reader["CarId"],
                                Brand = reader["Brand"].ToString(),
                                Model = reader["Model"].ToString(),
                                Year = (int)reader["Year"],
                                Color = reader["Color"].ToString(),
                                Transmission = reader["Transmission"].ToString(),
                                MainImagePath = reader["MainImagePath"] == DBNull.Value ? null : reader["MainImagePath"].ToString(),
                                Features = reader["Features"] == DBNull.Value ? null : reader["Features"].ToString(),
                                Status = reader["Status"].ToString(),
                                CreatedAt = (DateTime)reader["CreatedAt"]
                            };

                            car.RentalInfo = new RentalCar
                            {
                                CarId = car.CarId,
                                DailyRate = (decimal)reader["DailyRate"],
                                MonthlyRate = (decimal)reader["MonthlyRate"],
                                YearlyRate = (decimal)reader["YearlyRate"],
                                Notes = reader["RentalNotes"] == DBNull.Value ? null : reader["RentalNotes"].ToString()
                            };

                            cars.Add(car);
                        }
                    }
                }

                return cars;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب السيارات المعروضة للإيجار: {ex.Message}");
            }
        }

        public static bool AddCar(Car car, SaleCar saleInfo = null, RentalCar rentalInfo = null)
        {
            try
            {
                // إدراج السيارة
                string carQuery = @"
                    INSERT INTO Cars (Brand, Model, Year, Color, Transmission, MainImagePath, Defects, Features, OwnerId, Status)
                    VALUES (@brand, @model, @year, @color, @transmission, @mainImage, @defects, @features, @ownerId, @status);
                    SELECT SCOPE_IDENTITY();";

                var carParams = new SqlParameter[]
                {
                    new SqlParameter("@brand", car.Brand),
                    new SqlParameter("@model", car.Model),
                    new SqlParameter("@year", car.Year),
                    new SqlParameter("@color", car.Color),
                    new SqlParameter("@transmission", car.Transmission),
                    new SqlParameter("@mainImage", (object)car.MainImagePath ?? DBNull.Value),
                    new SqlParameter("@defects", (object)car.Defects ?? DBNull.Value),
                    new SqlParameter("@features", (object)car.Features ?? DBNull.Value),
                    new SqlParameter("@ownerId", (object)car.OwnerId ?? DBNull.Value),
                    new SqlParameter("@status", car.Status)
                };

                int carId = Convert.ToInt32(DatabaseHelper.ExecuteScalar(carQuery, carParams));

                // إدراج معلومات البيع إذا كانت موجودة
                if (saleInfo != null)
                {
                    string saleQuery = @"
                        INSERT INTO SaleCars (CarId, Price, IsNegotiable, OwnershipDocument, Notes)
                        VALUES (@carId, @price, @negotiable, @document, @notes)";

                    var saleParams = new SqlParameter[]
                    {
                        new SqlParameter("@carId", carId),
                        new SqlParameter("@price", saleInfo.Price),
                        new SqlParameter("@negotiable", saleInfo.IsNegotiable),
                        new SqlParameter("@document", (object)saleInfo.OwnershipDocument ?? DBNull.Value),
                        new SqlParameter("@notes", (object)saleInfo.Notes ?? DBNull.Value)
                    };

                    DatabaseHelper.ExecuteNonQuery(saleQuery, saleParams);
                }

                // إدراج معلومات الإيجار إذا كانت موجودة
                if (rentalInfo != null)
                {
                    string rentalQuery = @"
                        INSERT INTO RentalCars (CarId, DailyRate, MonthlyRate, YearlyRate, Notes)
                        VALUES (@carId, @daily, @monthly, @yearly, @notes)";

                    var rentalParams = new SqlParameter[]
                    {
                        new SqlParameter("@carId", carId),
                        new SqlParameter("@daily", rentalInfo.DailyRate),
                        new SqlParameter("@monthly", rentalInfo.MonthlyRate),
                        new SqlParameter("@yearly", rentalInfo.YearlyRate),
                        new SqlParameter("@notes", (object)rentalInfo.Notes ?? DBNull.Value)
                    };

                    DatabaseHelper.ExecuteNonQuery(rentalQuery, rentalParams);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة السيارة: {ex.Message}");
            }
        }
    }
}
