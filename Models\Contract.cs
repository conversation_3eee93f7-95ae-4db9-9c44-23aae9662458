using System;

namespace CarsSales.Models
{
    public class Contract
    {
        public int ContractId { get; set; }
        public int CarId { get; set; }
        public int CustomerId { get; set; }
        public string ContractType { get; set; } // Sale or Rental
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal Amount { get; set; }
        public decimal CommissionAmount { get; set; }
        public string Status { get; set; } // Active, Completed, Cancelled
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        public Car Car { get; set; }
        public Customer Customer { get; set; }

        public Contract()
        {
            CreatedAt = DateTime.Now;
            Status = "Active";
        }
    }
}