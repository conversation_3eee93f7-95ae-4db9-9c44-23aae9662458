# تطبيق إدارة مكتب السيارات - Cars Sales Management

## وصف المشروع
تطبيق WinForms لإدارة مكتب السيارات يدعم عمليات البيع والإيجار مع إدارة شاملة للعملاء والعقود والتقارير المالية.

## متطلبات النظام
- .NET Framework 4.8.1 أو أحدث
- SQL Server (تم الاختبار مع CR7)
- Visual Studio 2019 أو أحدث

## إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات
```sql
-- تشغيل هذا الأمر في SQL Server Management Studio
CREATE DATABASE CarsDB;
```

### 2. إنشاء الجداول
قم بتشغيل سكريبت `Database/CreateDatabase.sql` في قاعدة البيانات CarsDB

### 3. تحديث سلسلة الاتصال
تأكد من أن سلسلة الاتصال في `Data/DatabaseHelper.cs` تشير إلى الخادم الصحيح:
```csharp
private static readonly string connectionString = @"Data Source=CR7;Initial Catalog=CarsDB;Integrated Security=True";
```

## هيكل المشروع

### المجلدات الرئيسية:
- **Data/**: فئات الاتصال بقاعدة البيانات
- **Models/**: نماذج البيانات (Car, Customer, Contract, إلخ)
- **Forms/**: واجهات المستخدم
- **Services/**: خدمات الأعمال
- **Database/**: سكريبتات قاعدة البيانات

### الجداول الرئيسية:
- **Managers**: بيانات المدراء
- **Customers**: بيانات العملاء
- **Cars**: السيارات الأساسية
- **CarImages**: صور السيارات
- **SaleCars**: السيارات المعروضة للبيع
- **RentalCars**: السيارات المعروضة للإيجار
- **Contracts**: العقود
- **Reservations**: الحجوزات المؤقتة
- **FinancialTransactions**: العمليات المالية
- **CarOwners**: أصحاب السيارات المودعة

## الميزات الرئيسية

### للمدير:
- إدارة السيارات (إضافة، تعديل، حذف)
- إدارة العملاء
- إدارة العقود
- التقارير المالية
- إدارة الحجوزات

### للعملاء:
- تسجيل حساب جديد
- تسجيل الدخول
- عرض السيارات المتاحة للبيع
- عرض السيارات المتاحة للإيجار
- حجز السيارات

## بيانات الدخول الافتراضية
- **المدير**: 
  - اسم المستخدم: admin
  - كلمة المرور: admin123

## ملاحظات التطوير
- تم استخدام ADO.NET للتعامل مع قاعدة البيانات
- تم تصميم الواجهات باستخدام Windows Forms
- يدعم التطبيق اللغة العربية
- تم تطبيق مبادئ فصل الطبقات (Separation of Concerns)

## كيفية التشغيل
1. افتح المشروع في Visual Studio
2. تأكد من إعداد قاعدة البيانات كما هو موضح أعلاه
3. اضغط F5 لتشغيل التطبيق
4. استخدم بيانات المدير الافتراضية للدخول

## استكشاف الأخطاء
- تأكد من أن SQL Server يعمل
- تحقق من سلسلة الاتصال
- تأكد من وجود قاعدة البيانات CarsDB
- تحقق من صلاحيات المستخدم للوصول إلى قاعدة البيانات
